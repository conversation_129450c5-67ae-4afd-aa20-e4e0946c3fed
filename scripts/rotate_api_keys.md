# API Key Rotation Guide

## 🔄 Safer Alternative to Git History Rewriting

Instead of rewriting git history (which can be risky), the recommended approach is to **rotate your API keys**. This makes the exposed keys useless while keeping your git history intact.

## 📋 Step-by-Step API Key Rotation

### 1. Firebase Console - Web App

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `sufiyan-sakkeer`
3. Go to **Project Settings** (gear icon)
4. Scroll to **Your apps** section
5. Find your **Web app** and click the config icon
6. Click **Regenerate API Key** or create a new web app
7. Copy the new configuration

### 2. Firebase Console - Android App

1. In the same **Your apps** section
2. Find your **Android app**
3. Download new `google-services.json`
4. Extract the new API key from the file

### 3. Firebase Console - iOS App

1. Find your **iOS app** in **Your apps**
2. Download new `GoogleService-Info.plist`
3. Extract the new API key from the file

### 4. Update Your Environment File

Create `.env` file with new keys:

```bash
# Copy the example file
cp .env.example .env

# Edit .env with your NEW API keys
nano .env  # or use your preferred editor
```

Fill in the new values:
```env
FIREBASE_PROJECT_ID=sufiyan-sakkeer
FIREBASE_MESSAGING_SENDER_ID=804519308110
FIREBASE_AUTH_DOMAIN=sufiyan-sakkeer.firebaseapp.com
FIREBASE_STORAGE_BUCKET=sufiyan-sakkeer.firebasestorage.app

# NEW API KEYS (replace with your new ones)
FIREBASE_WEB_API_KEY=your-new-web-api-key
FIREBASE_WEB_APP_ID=your-new-web-app-id
FIREBASE_WEB_MEASUREMENT_ID=your-new-measurement-id

FIREBASE_ANDROID_API_KEY=your-new-android-api-key
FIREBASE_ANDROID_APP_ID=your-new-android-app-id

FIREBASE_IOS_API_KEY=your-new-ios-api-key
FIREBASE_IOS_APP_ID=your-new-ios-app-id
FIREBASE_IOS_BUNDLE_ID=com.example.portfolio
```

### 5. Revoke Old API Keys

**IMPORTANT**: After updating your app with new keys and verifying everything works:

1. Go back to Firebase Console
2. Find the old web app configuration
3. **Delete the old web app** or **regenerate its keys again**
4. This makes the exposed keys completely useless

### 6. Update Your Code

Replace the hardcoded configuration in your code:

#### In `lib/main.dart`:
```dart
// Replace this import
// import 'firebase_options.dart';

// With this import
import 'config/firebase_config.dart';

// Replace this line
// await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

// With this line
await Firebase.initializeApp(options: SecureFirebaseConfig.currentPlatform);
```

#### In `web/index.html`:
Remove or comment out the hardcoded Firebase configuration:
```html
<!-- Remove or comment out this entire section -->
<!--
<script>
  const firebaseConfig = {
    apiKey: "AIzaSyDpZwHG4wVOUyfHXWiojDOeT6KSKMH_pbo",
    // ... rest of config
  };
  firebase.initializeApp(firebaseConfig);
</script>
-->
```

### 7. Test Your Application

1. Run your app locally: `flutter run`
2. Verify Firebase functionality works
3. Check browser console for any Firebase errors
4. Test on different platforms (web, mobile)

### 8. Deploy with New Configuration

When deploying to production, ensure your hosting platform has the environment variables set:

- **Netlify**: Set environment variables in Site Settings
- **Vercel**: Set environment variables in Project Settings
- **Firebase Hosting**: Use Firebase Functions config
- **GitHub Actions**: Set repository secrets

## ✅ Verification Checklist

- [ ] New API keys generated in Firebase Console
- [ ] `.env` file created with new keys
- [ ] Code updated to use `SecureFirebaseConfig`
- [ ] Web configuration removed from `index.html`
- [ ] Application tested and working
- [ ] Old API keys revoked/deleted
- [ ] Production environment variables configured

## 🎯 Benefits of This Approach

1. **No git history rewriting** - safer for teams
2. **Immediate security** - old keys become useless
3. **No coordination needed** - team members don't need to reset their repos
4. **Reversible** - can always go back if issues arise
5. **Best practice** - regular key rotation is recommended

## 🚨 Emergency: If Keys Are Already Compromised

If you suspect the exposed keys are being misused:

1. **Immediately revoke** the old keys in Firebase Console
2. **Monitor** your Firebase usage for unusual activity
3. **Check** Firebase Authentication logs
4. **Review** Firestore security rules
5. **Consider** enabling additional security features

## 📞 Need Help?

- [Firebase Security Best Practices](https://firebase.google.com/docs/projects/api-keys#api-keys-for-firebase-are-different)
- [Firebase Console](https://console.firebase.google.com/)
- [Flutter Environment Variables](https://pub.dev/packages/flutter_dotenv)
