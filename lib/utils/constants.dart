class AppConstants {
  // Personal Information
  static const String name = '<PERSON><PERSON>';
  static const String title = 'Flutter Developer';
  static const String email = '<EMAIL>';
  static const String phone = '+91 **********';
  static const String location = 'Bangalore, India';
  static const String aboutMe =
      'Results-driven Flutter developer with 2 years of experience in building '
      'responsive, visually appealing, and high-performance mobile applications. '
      'Proficient in Dart and well-versed in Flutter\'s ecosystem, including state '
      'management (BLoC, Getx, Provider), WebSocket integration, Firebase, and deep linking. '
      'Passionate about delivering seamless user experiences and optimizing app performance. '
      'Eager to contribute to dynamic teams and tackle challenging mobile app development projects.';

  // Social Media Links
  static const String githubUrl = 'https://github.com/sufiyansakkeer';
  static const String linkedinUrl =
      'https://www.linkedin.com/in/sufiyan-sakkeer/';
  static const String twitterUrl = '';
  static const String instagramUrl = '';

  // Resume
  static const String resumeFileName = 'sufiyan_sakke<PERSON>_resume.pdf';
  static const String resumeUrl =
      'https://drive.google.com/file/d/1FyMehryJueYg9_V2zhWNBtva_tlNOj6s/view?usp=sharing';

  // Contact Form
  static const String contactFormEndpoint =
      'https://formspree.io/f/your-form-id';

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 500);
  static const Duration longAnimationDuration = Duration(milliseconds: 800);
}
