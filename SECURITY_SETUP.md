# Security Setup Guide

This guide explains how to properly configure sensitive data and API keys for this Flutter project.

## 🚨 Security Issues Found

The following security vulnerabilities were identified and addressed:

1. **Firebase API keys exposed** in `lib/firebase_options.dart`
2. **Firebase configuration hardcoded** in `web/index.html`
3. **Missing .gitignore patterns** for sensitive files

## 🔒 Security Improvements Implemented

### 1. Updated .gitignore

Added comprehensive patterns to prevent sensitive files from being committed:
- Firebase configuration files
- Environment files (.env, .env.*)
- API key files
- Authentication certificates
- Platform-specific sensitive files

### 2. Environment-Based Configuration

Created `lib/config/firebase_config.dart` to replace hardcoded API keys with environment variables.

### 3. Environment File Template

Created `.env.example` as a template for environment variables.

## 🛠️ Setup Instructions

### Step 1: Create Environment File

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Fill in your actual Firebase configuration values in `.env`

### Step 2: Update Your Code

Replace usage of `DefaultFirebaseOptions.currentPlatform` with `SecureFirebaseConfig.currentPlatform`:

```dart
// Before (insecure)
import 'firebase_options.dart';
await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

// After (secure)
import 'config/firebase_config.dart';
await Firebase.initializeApp(options: SecureFirebaseConfig.currentPlatform);
```

### Step 3: Update Web Configuration

Update `web/index.html` to load Firebase configuration from environment variables instead of hardcoded values.

### Step 4: Install Environment Variable Package

Add flutter_dotenv to your pubspec.yaml:
```yaml
dependencies:
  flutter_dotenv: ^5.1.0
```

Then load environment variables in your main.dart:
```dart
import 'package:flutter_dotenv/flutter_dotenv.dart';

Future<void> main() async {
  await dotenv.load(fileName: ".env");
  runApp(MyApp());
}
```

## 🗑️ Removing Sensitive Data from Git History

**CRITICAL**: The files `lib/firebase_options.dart` and `web/index.html` contain exposed API keys and are already committed to git history.

### Option 1: Remove from Git History (Recommended)

```bash
# Remove firebase_options.dart from git history
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch lib/firebase_options.dart' \
  --prune-empty --tag-name-filter cat -- --all

# Remove sensitive content from web/index.html
# You'll need to manually edit the file to remove the Firebase config section
# Then commit the cleaned version

# Force push to update remote repository
git push origin --force --all
git push origin --force --tags
```

### Option 2: Rotate API Keys (Safer)

1. Go to your Firebase Console
2. Generate new API keys for all platforms
3. Update your `.env` file with new keys
4. Revoke the old API keys in Firebase Console
5. Update your code to use the new secure configuration

## 🔍 Verification

After implementing these changes:

1. Ensure `.env` file is not tracked by git:
   ```bash
   git status  # .env should not appear in tracked files
   ```

2. Verify no sensitive data in code:
   ```bash
   grep -r "AIzaSy" . --exclude-dir=.git --exclude="*.md"
   ```

3. Check that environment variables are loaded correctly in your app

## 📋 Security Checklist

- [ ] `.env` file created with actual values
- [ ] `.env` file added to .gitignore (already done)
- [ ] Code updated to use `SecureFirebaseConfig`
- [ ] Web configuration updated to use environment variables
- [ ] Old API keys rotated in Firebase Console
- [ ] Sensitive files removed from git history
- [ ] Team members informed about new security practices

## 🚨 Important Notes

1. **Never commit `.env` files** to version control
2. **Rotate API keys** that were previously exposed
3. **Use different API keys** for different environments (dev, staging, prod)
4. **Regularly audit** your codebase for hardcoded secrets
5. **Use CI/CD environment variables** for deployment

## 📞 Support

If you need help implementing these security measures, please refer to:
- [Firebase Security Documentation](https://firebase.google.com/docs/projects/api-keys)
- [Flutter Environment Variables Guide](https://pub.dev/packages/flutter_dotenv)
