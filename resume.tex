\documentclass[11pt,a4paper,sans]{moderncv}
\moderncvstyle{banking}
\moderncvcolor{black}
\nopagenumbers{}
\usepackage[utf8]{inputenc}
\usepackage{ragged2e}
\usepackage[scale=0.915]{geometry}
\usepackage{multicol}
\usepackage{enumitem}
\usepackage{amssymb}
\usepackage{xcolor}
\usepackage[unicode]{hyperref}
\name{Sufiyan}{Sakkeer}

% Custom cventry
\newcommand*{\customcventry}[7][.13em]{
\begin{tabular}{@{}l}
{\bfseries #4} \\
{\itshape #3}
\end{tabular}
\hfill
\begin{tabular}{l@{}}
{\bfseries #5} \\
{\itshape #2}
\end{tabular}
\ifx&#7&%
\else{\\
\begin{minipage}{\maincolumnwidth}%
\small#7%
\end{minipage}}\fi%
\par\addvspace{#1}}

\begin{document}
\makecvtitle
\vspace*{-16mm}
\begin{center}\textbf{Flutter Developer}\end{center}
\begin{center}
    <EMAIL> 
    \enspace \textbar \enspace
    +91 ********** 
    \enspace \textbar \enspace
    \href{https://www.linkedin.com/in/sufiyan-sakkeer/}{\color{blue}LinkedIn} 
    \enspace \textbar \enspace
    \href{https://github.com/sufiyansakkeer}{\color{blue}GitHub}
    \enspace \textbar \enspace
    \href{https://sufiyan-sakkeer.netlify.app/}{\color{blue}Portfolio}
\end{center}

\section{Profile}
Results-driven Flutter developer with 2 years of experience in building responsive, high-performance mobile applications. Skilled in Dart and the Flutter ecosystem, including state management (BLoC, GetX, Provider), WebSocket integration, Firebase, deep linking, and CI/CD pipelines. Passionate about delivering seamless user experiences, optimizing performance, and automating workflows. 

\section{Experience}
\customcventry{06/2025 – Present}{\href{https://mec.edu.om/en/}{Middle East College}}{Flutter Developer}{Muscat, Oman}{}{
\begin{itemize}[leftmargin=0.6cm, label={\textbullet}]
\item Developing scalable Flutter applications tailored to educational and institutional requirements.
\item Maintaining clean code architecture and applying modern Flutter best practices.
\end{itemize}}

\vspace{0.7em}

\customcventry{07/2023 – 05/2025}{\href{https://avua.com/}{avua International Pvt. Ltd.}}{Flutter Developer}{Chandigarh, India}{}{
\textbf{avua} is an AI-based recruitment platform with 100k+ active users.
\begin{itemize}[leftmargin=0.6cm, label={\textbullet}]
\item Led the design and development of two cross-platform applications using Flutter.
\item Collaborated with backend engineers for seamless integration of REST APIs.
\item Spearheaded architecture improvements, achieving a 35\% boost in performance.
\item Reduced time-to-market by 30\% through optimized development practices.
\item Delivered features such as real-time chat, push notifications, and deep linking.
\end{itemize}}

\section{Skills}
\begin{itemize}[label=\textbullet]
\item \textbf{Languages:} Dart, JavaScript, HTML, CSS
\item \textbf{State Management:} BLoC, Provider, GetX
\item \textbf{Mobile Development:} Flutter, Firebase (Firestore, Auth, FCM), REST API, WebSocket, Deep Linking
\item \textbf{Tools:} Git, GitHub, GitHub Actions, Jira, Postman, Figma, Android Studio, Xcode, VS Code
\item \textbf{Cloud/CI-CD:} Firebase, GitHub Actions, Play Store \& App Store Deployment
\item \textbf{Core CS:} Data Structures \& Algorithms, Object-Oriented Programming
\end{itemize}

\section{Projects}
\customcventry{}{avua – Applicant App (\href{https://apps.apple.com/in/app/avua/id6469672021}{App Store}, \href{https://play.google.com/store/apps/details?id=com.app.avua}{Play Store})}{}{}{}{
\begin{itemize}[leftmargin=0.6cm, label={\textbullet}]
\item Built AI-driven recruitment features: resume builder, video resumes, and applicant scoring.
\item Integrated OAuth with JWT, WebSocket for messaging, and Deep Linking.
\item Boosted user activity by 40\% in 3 months post-launch.
\item Tech stack: Flutter, REST API, FCM, OAuth, BLoC, WebSocket.
\end{itemize}}

\customcventry{}{avua Recruiter (\href{https://apps.apple.com/in/app/avua-recruiter/id6474672130}{App Store}, \href{https://play.google.com/store/apps/details?id=com.employer.app}{Play Store})}{}{}{}{
\begin{itemize}[leftmargin=0.6cm, label={\textbullet}]
\item Developed recruiter-side features including job posting, applicant search, and avua Pool.
\item Implemented REST API with pagination, WebSocket messaging, and flavors.
\item Tech stack: Flutter, REST API, OAuth, Deep Linking, BLoC, Flavors.
\end{itemize}}

\customcventry{}{Money Track (\href{https://github.com/sufiyansakkeer/Money-Tracker/}{GitHub})}{}{}{}{
\begin{itemize}[leftmargin=0.6cm, label={\textbullet}]
\item Personal finance manager with budget planning, analytics, and real-time currency conversion.
\item Implemented BLoC architecture, Hive database, Syncfusion Charts, and CI/CD pipelines.
\item Added animations (Rive/Lottie) and custom dependency injection for modularity.
\end{itemize}}

\customcventry{}{BSocial – Social Media App (\href{https://github.com/sufiyansakkeer/BSocial}{GitHub})}{}{}{}{
\begin{itemize}[leftmargin=0.6cm, label={\textbullet}]
\item Feature-rich app with feeds, chat, profile customization, and follow system.
\item Integrated Firebase Authentication, Crashlytics, and push notifications.
\end{itemize}}

\section{Education}
\customcventry{2019 – 2022}{\href{https://bgscollege.com/}{BGS \& SJB Group of Institutions}}{Bachelor of Computer Applications}{Bangalore, India}{}{}
\customcventry{October 2022 – July 2023}{\href{https://www.brototype.com/}{Brototype}}{Flutter Development Internship}{Calicut, India}{}{}

\section{Publications}
\customcventry{2024}{\href{https://www.linkedin.com/pulse/architecting-brilliance-unveiling-power-solid-software-sakkeer-0icjc/}{Architecting Brilliance: Unveiling the Power of SOLID Principles in Software Development}}{}{}{}{}

\end{document}
