# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/linux/
/macos/
/windows/
# Android Studio related
.idea/
.gradle/
/build/
/.qodo/
/android/
/ios/

# Security and Sensitive Data
# Firebase configuration files with API keys
lib/firebase_options.dart
firebase_options.dart
google-services.json
GoogleService-Info.plist

# Environment files
.env
.env.local
.env.development
.env.staging
.env.production
.env.test
.env.*

# API keys and secrets
**/api_keys.dart
**/secrets.dart
**/config/secrets.*
**/config/api_keys.*
**/*_secrets.*
**/*_api_keys.*

# Authentication and certificates
*.key
*.pem
*.p12
*.keystore
*.jks
*.crt
*.cer
*.pfx

# Configuration files that might contain secrets
config.json
secrets.json
credentials.json
service-account*.json
*-service-account.json

# Platform-specific sensitive files
# Android
android/app/google-services.json
android/key.properties
android/keystore.properties
android/app/src/main/res/values/secrets.xml

# iOS
ios/Runner/GoogleService-Info.plist
ios/firebase_app_id_file.json
ios/Runner/Info.plist.backup

# Web
web/firebase-config.js
web/config.js
web/secrets.js

# Backup files that might contain sensitive data
*.backup
*.bak
*.orig
*.tmp
